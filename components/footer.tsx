import Link from 'next/link'
import Image from 'next/image'
import { Facebook, Twitter, Instagram, Youtube, Linkedin, Mail, Phone, MapPin } from 'lucide-react'

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Logo and Description */}
          <div className="lg:col-span-1">
            <Link href="/" className="inline-block mb-4">
              <Image
                src="/images/dca-logo.png"
                alt="Del York Creative Academy"
                width={150}
                height={80}
                className="h-16 w-auto"
              />
            </Link>
            <p className="text-gray-300 text-sm mb-4">
              Africa's premier institution for creative education, nurturing the next generation of creative professionals.
            </p>
            <div className="flex space-x-4">
              <a href="https://facebook.com/delyorkcreativeacademy" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="https://twitter.com/DelyorkAcademy" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white">
                <Twitter className="h-5 w-5" />
              </a>
              <a href='https://www.instagram.com/delyorkcreativeacademy/' target="_blank" rel="noopener noreferrer"  className="text-gray-400 hover:text-white">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="https://www.youtube.com/user/DelYorkIntl" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white">
                <Youtube className="h-5 w-5" />
              </a>
              <a href="https://www.linkedin.com/showcase/delyorkcreativeacademy" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white">
                <Linkedin className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* About */}
          <div>
            <h3 className="text-lg font-semibold mb-4">About</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/about" className="text-gray-300 hover:text-white">About Us</Link></li>
              {/* <li><Link href="/about/history" className="text-gray-300 hover:text-white">Our History</Link></li> */}
              <li><Link href="/about/what-we-do" className="text-gray-300 hover:text-white">Mission & Vision</Link></li>
              {/* <li><Link href="/about/facil  2ities" className="text-gray-300 hover:text-white">Facilities</Link></li> */}
              {/* <li><Link href="/about/partnerships" className="text-gray-300 hover:text-white">Industry Partners</Link></li> */}
              {/* <li><Link href="/about/accreditation" className="text-gray-300 hover:text-white">Accreditation</Link></li> */}
              {/* <li><Link href="/careers" className="text-gray-300 hover:text-white">Careers</Link></li> */}
            </ul>
          </div>

          {/* Programs & Courses */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Programs</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/courses/filmmaking" className="text-gray-300 hover:text-white">Filmmaking</Link></li>
              <li><Link href="/courses/acting" className="text-gray-300 hover:text-white">Acting & Performance</Link></li>
              <li><Link href="/courses/screenwriting" className="text-gray-300 hover:text-white">Screenwriting</Link></li>
              <li><Link href="/courses/digital-media" className="text-gray-300 hover:text-white">Digital Media</Link></li>
              <li><Link href="/special-programs/masterclass" className="text-gray-300 hover:text-white">Masterclass Series</Link></li>
              <li><Link href="/special-programs/workshops" className="text-gray-300 hover:text-white">Workshops</Link></li>
              <li><Link href="/spe" className="text-gray-300 hover:text-white">Online Programs</Link></li>
            </ul>
          </div>

          {/* Resources & Support */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Resources</h3>
            <ul className="space-y-2 text-sm">
              {/* <li><Link href="/news" className="text-gray-300 hover:text-white">News & Updates</Link></li> */}
              {/* <li><Link href="/blog" className="text-gray-300 hover:text-white">Blog</Link></li> */}
              <li><Link href="/events" className="text-gray-300 hover:text-white">Events</Link></li>
              {/* <li><Link href="/gallery" className="text-gray-300 hover:text-white">Gallery</Link></li> */}
              <li><Link href="/testimonials" className="text-gray-300 hover:text-white">Testimonials</Link></li>
              <li><Link href="/faq" className="text-gray-300 hover:text-white">FAQ</Link></li>
              {/* <li><Link href="/downloads" className="text-gray-300 hover:text-white">Downloads</Link></li> */}
            </ul>
          </div>

          {/* Contact & Admissions */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Get Started</h3>
            <ul className="space-y-2 text-sm mb-6">
              <li><Link href="https://portal.delyorkcreative.academy/student/apply" target="_blank" rel="noopener noreferrer" className="text-gray-300 hover:text-white">Apply Now</Link></li>
              {/* <li><Link href="/admissions/requirements" className="text-gray-300 hover:text-white">Requirements</Link></li>
              <li><Link href="/admissions/tuition" className="text-gray-300 hover:text-white">Tuition & Fees</Link></li>
              <li><Link href="/admissions/scholarships" className="text-gray-300 hover:text-white">Scholarships</Link></li> */}
              <li><Link href="/contact" className="text-gray-300 hover:text-white">Contact Us</Link></li>
            </ul>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-center text-gray-300">
                <Phone className="h-4 w-4 mr-2" />
                <div className='flex items-center flex-col'>
                <a href="tel:+2349133820024">+234 ************</a>
                <a href="tel:+2349165666255">+234 ************</a>
                <a href="tel:+2349047218313">+234 ************</a>
                </div>
              </div>
              <div className="flex items-center text-gray-300">
                <Mail className="h-4 w-4 mr-2" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-start text-gray-300">
                <MapPin className="h-4 w-4 mr-2 mt-0.5" />
                <span>108 Abibu Adetoro Street, Opposite Mt Horeb Pharmacy, Victoria Island 106104, Lagos.</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-400 mb-4 md:mb-0">
              © 2024 Del York Creative Academy. All rights reserved.
            </div>
            <div className="flex space-x-6 text-sm">
              <Link href="/privacy" className="text-gray-400 hover:text-white">Privacy Policy</Link>
              <Link href="/terms" className="text-gray-400 hover:text-white">Terms of Service</Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white">Cookie Policy</Link>
              <Link href="/accessibility" className="text-gray-400 hover:text-white">Accessibility</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
